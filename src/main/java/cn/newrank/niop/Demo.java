package cn.newrank.niop;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 演示占位符处理功能
 */
public class Demo {
    
    public static void main(String[] args) {
        System.out.println("=== 占位符处理演示 ===\n");
        
        // 原始查询（前端传来的）
        String originalQuery = "select * from api_custom_ant_research_dy_account_daily_${env.date} where status = 'active'";
        
        System.out.println("原始查询:");
        System.out.println(originalQuery);
        
        // 处理占位符
        String processedQuery = processPlaceholders(originalQuery);
        
        System.out.println("\n处理后的HOCON格式:");
        System.out.println(processedQuery);
        
        // 展示完整的HOCON配置
        String hoconConfig = String.format("""
            {
                env : {
                    date : "********"
                },
                source : {
                    mysql : {
                        query : %s,
                        output : "mysql",
                        dsId : "F89FC"
                    }
                }
            }
            """, processedQuery);
        
        System.out.println("\n完整的HOCON配置:");
        System.out.println(hoconConfig);
        
        System.out.println("\n解析后的最终查询应该是:");
        System.out.println("select * from api_custom_ant_research_dy_account_daily_******** where status = 'active'");
    }
    
    /**
     * 处理包含占位符的字符串，将占位符移到引号外以支持HOCON解析
     */
    public static String processPlaceholders(String originalValue) {
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(originalValue);
        
        if (!matcher.find()) {
            return "\"" + originalValue.replace("\"", "\\\"") + "\"";
        }
        
        StringBuilder result = new StringBuilder();
        int lastEnd = 0;
        matcher.reset();
        
        while (matcher.find()) {
            // 添加占位符前的字符串部分
            if (matcher.start() > lastEnd) {
                String beforePlaceholder = originalValue.substring(lastEnd, matcher.start());
                if (!beforePlaceholder.isEmpty()) {
                    result.append("\"").append(beforePlaceholder.replace("\"", "\\\"")).append("\"");
                }
            }
            
            // 添加空格分隔符（如果需要）
            if (result.length() > 0) {
                result.append(" ");
            }
            
            // 添加占位符
            result.append("${").append(matcher.group(1)).append("}");
            lastEnd = matcher.end();
            
            // 如果后面还有内容，添加空格
            if (lastEnd < originalValue.length()) {
                result.append(" ");
            }
        }
        
        // 添加最后剩余的部分
        if (lastEnd < originalValue.length()) {
            String afterLastPlaceholder = originalValue.substring(lastEnd);
            if (!afterLastPlaceholder.isEmpty()) {
                result.append("\"").append(afterLastPlaceholder.replace("\"", "\\\"")).append("\"");
            }
        }
        
        return result.toString();
    }
}
