package cn.newrank.niop;

/**
 * 测试占位符处理功能
 */
public class PlaceholderTest {
    
    public static void main(String[] args) {
        System.out.println("=== 测试占位符处理功能 ===\n");
        
        // 测试用例1：占位符在字符串末尾
        testCase("select * from table_${env.date}", 
                "占位符在末尾");
        
        // 测试用例2：占位符在字符串开头
        testCase("${env.prefix}_table_name", 
                "占位符在开头");
        
        // 测试用例3：占位符在字符串中间
        testCase("select * from ${env.schema}.table_${env.date}", 
                "多个占位符");
        
        // 测试用例4：复杂SQL查询
        testCase("select * from api_custom_ant_research_dy_account_daily_${env.date} where status = 'active' and created_date >= '${env.start_date}' and region = '${env.region}'", 
                "复杂SQL查询");
        
        // 测试用例5：没有占位符
        testCase("select * from static_table where id = 1", 
                "没有占位符");
        
        // 测试用例6：包含引号的字符串
        testCase("select * from table where name = \"test_${env.date}\" and status = 'active'", 
                "包含引号的字符串");
    }
    
    private static void testCase(String input, String description) {
        System.out.println("测试: " + description);
        System.out.println("输入: " + input);
        String result = TaskDispatchMain.processPlaceholders(input);
        System.out.println("输出: " + result);
        System.out.println("---");
    }
}
