package cn.newrank.niop.pojo;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2022/11/29 20:24
 * @version: 1.0.0
 * @description:
 */
public class TaskConfig {

    private Long id;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 目标任务id
     */
    private String taskId;

    /**
     * 父节点
     */
    private List<Task> parent;

    /**
     * 子节点
     */
    private List<Task> child;

}