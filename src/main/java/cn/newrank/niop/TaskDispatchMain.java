package cn.newrank.niop;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class TaskDispatchMain {

    public static void main(String[] args) {

        String hocon = """
                {
                         "env" : {
                            "date": "********"
                         }
                         "source": {
                             "mysql": {
                                 "output": "mysql",
                                 "dsId": "F89FC",
                                 "query": "select * from api_custom_ant_research_dy_account_daily_${env.date}"
                             }
                         },
                         "transform": {
                             "mapper": {
                                 "input": "mysql",
                                 "output": "default",
                                 "field_mapper": {
                                     "uid": "uid",
                                     "account": "account",
                                     "sec_uid": "secUid",
                                     "nickname": "nickname"
                                 }
                             }
                         },
                         "sink": {
                             "oss": {
                                 "dsId": "7513402d",
                                 "input": "default",
                                 "bucket": "yd-api-test",
                                 "path": "api/default_15/",
                                 "file_type": "csv",
                                 "file_name": "ant-research.csv"
                             }
                         },
                         "status": {}
                     }
                """;

        String newHocon = processQueryWithPlaceholders(hocon);

        System.err.println(newHocon);

        Config config = ConfigFactory.parseString(newHocon);
        Config resolve = config.resolve();

        System.err.println(resolve.getString("env.date"));
        System.err.println(resolve.getString("source.mysql.query"));

    }


    public static String processQueryWithPlaceholders(String originalQuery) {
        // 查找所有的 ${...} 占位符
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(originalQuery);

        if (!matcher.find()) {
            // 没有占位符，直接返回带引号的查询
            return originalQuery;
        }

        // 有占位符，需要拆分字符串
        StringBuilder result = new StringBuilder();
        int lastEnd = 0;
        matcher.reset();

        while (matcher.find()) {
            // 添加占位符前的字符串部分（带引号）
            if (matcher.start() > lastEnd) {
                String beforePlaceholder = originalQuery.substring(lastEnd, matcher.start());
                if (!beforePlaceholder.isEmpty()) {
                    result.append("\"").append(beforePlaceholder).append("\" ");
                }
            }

            // 添加占位符（不带引号）
            result.append("${").append(matcher.group(1)).append("} ");
            lastEnd = matcher.end();
        }

        // 添加最后剩余的字符串部分
        if (lastEnd < originalQuery.length()) {
            String afterLastPlaceholder = originalQuery.substring(lastEnd);
            if (!afterLastPlaceholder.isEmpty()) {
                result.append("\"").append(afterLastPlaceholder).append("\"");
            }
        }

        return result.toString().trim();
    }

}