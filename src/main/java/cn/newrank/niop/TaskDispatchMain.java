package cn.newrank.niop;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
public class TaskDispatchMain {

    /**
     * 处理包含占位符的字符串，将占位符移到引号外以支持HOCON解析
     * @param originalValue 原始字符串值（可能包含占位符）
     * @return 处理后的HOCON格式字符串
     */
    public static String processPlaceholders(String originalValue) {
        // 查找所有的 ${...} 占位符
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)\\}");
        Matcher matcher = pattern.matcher(originalValue);

        if (!matcher.find()) {
            // 没有占位符，直接返回带引号的字符串
            return "\"" + originalValue.replace("\"", "\\\"") + "\"";
        }

        // 有占位符，需要拆分字符串
        StringBuilder result = new StringBuilder();
        int lastEnd = 0;
        matcher.reset();

        while (matcher.find()) {
            // 添加占位符前的字符串部分（带引号）
            if (matcher.start() > lastEnd) {
                String beforePlaceholder = originalValue.substring(lastEnd, matcher.start());
                if (!beforePlaceholder.isEmpty()) {
                    // 转义引号并添加到结果中
                    result.append("\"").append(beforePlaceholder.replace("\"", "\\\"")).append("\"");
                }
            }

            // 如果前面有内容，添加空格分隔
            if (result.length() > 0) {
                result.append(" ");
            }

            // 添加占位符（不带引号）
            result.append("${").append(matcher.group(1)).append("}");
            lastEnd = matcher.end();

            // 如果后面还有内容，添加空格分隔
            if (lastEnd < originalValue.length()) {
                result.append(" ");
            }
        }

        // 添加最后剩余的字符串部分
        if (lastEnd < originalValue.length()) {
            String afterLastPlaceholder = originalValue.substring(lastEnd);
            if (!afterLastPlaceholder.isEmpty()) {
                result.append("\"").append(afterLastPlaceholder.replace("\"", "\\\"")).append("\"");
            }
        }

        return result.toString();
    }

    /**
     * 处理整个JSON/HOCON字符串中的占位符
     * @param jsonString 原始JSON字符串
     * @return 处理后的HOCON字符串
     */
    public static String processJsonWithPlaceholders(String jsonString) {
        // 这里我们需要找到所有包含占位符的字符串值并处理它们
        // 使用正则表达式匹配 "key": "value with ${placeholder}" 的模式
        Pattern pattern = Pattern.compile("(\"[^\"]*\"\\s*:\\s*)(\"[^\"]*\\$\\{[^}]+\\}[^\"]*\")");
        Matcher matcher = pattern.matcher(jsonString);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group(1); // "key":
            String value = matcher.group(2); // "value with ${placeholder}"

            // 去掉值两边的引号
            String valueContent = value.substring(1, value.length() - 1);

            // 处理占位符
            String processedValue = processPlaceholders(valueContent);

            // 替换整个匹配项
            matcher.appendReplacement(result, key + processedValue);
        }
        matcher.appendTail(result);

        return result.toString();
    }

    public static void main(String[] args) {
        // 模拟前端传来的JSON字符串（包含占位符）
        String originalJson = """
                {
                         "env" : {
                            "date": "********"
                         },
                         "source": {
                             "mysql": {
                                 "output": "mysql",
                                 "dsId": "F89FC",
                                 "query": "select * from api_custom_ant_research_dy_account_daily_${env.date}"
                             }
                         },
                         "transform": {
                             "mapper": {
                                 "input": "mysql",
                                 "output": "default",
                                 "field_mapper": {
                                     "uid": "uid",
                                     "account": "account",
                                     "sec_uid": "secUid",
                                     "nickname": "nickname"
                                 }
                             }
                         },
                         "sink": {
                             "oss": {
                                 "dsId": "7513402d",
                                 "input": "default",
                                 "bucket": "yd-api-test",
                                 "path": "api/default_15/",
                                 "file_type": "csv",
                                 "file_name": "ant-research.csv"
                             }
                         },
                         "status": {}
                     }
                """;

        System.out.println("=== 原始JSON ===");
        System.out.println(originalJson);

        // 处理占位符
        String processedHocon = processJsonWithPlaceholders(originalJson);

        System.out.println("\n=== 处理后的HOCON ===");
        System.out.println(processedHocon);

        // 解析配置
        Config config = ConfigFactory.parseString(processedHocon);
        Config resolved = config.resolve();

        System.out.println("\n=== 解析结果 ===");
        System.out.println("env.date: " + resolved.getString("env.date"));
        System.out.println("source.mysql.query: " + resolved.getString("source.mysql.query"));

    }

}