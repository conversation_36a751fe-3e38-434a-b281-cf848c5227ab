package cn.newrank.niop;

import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import com.typesafe.config.ConfigResolveOptions;

/**
 * <AUTHOR>
 */
public class TaskDispatchMain {

    public static void main(String[] args) {

        String hocon = """
                {
                        env : {
                            date : "20250807"
                        },
                        sink : {
                            oss : {
                                ak : "LTAI5tEdL4gqFto92doJEH15",
                                bucket : "yd-api-test",
                                endpoint : "oss-cn-hangzhou.aliyuncs.com",
                                file_name : "test.csv",
                                file_type : "csv",
                                input : "default",
                                path : "api/default_15/",
                                region : "cn-hangzhou",
                                sk : "******************************"
                            }
                        },
                        source : {
                            mysql : {
                                output : "mysql",
                                password : "nwig2d89saf2sFssa",
                                // 占位符在引号内，但现在能被解析了
                                query : "select * from api_custom_ant_research_dy_account_daily_${env.date}",
                                url : "******************************************************************************************************",
                                username : "api_test"
                            }
                        },
                        transform : {
                            mapper : {
                                field_mapper : {
                                    account : "account",
                                    nickname : "nickname",
                                    sec_uid : "secUid",
                                    uid : "uid"
                                },
                                input : "mysql",
                                output : "default"
                            }
                        }
                    }
                """;
        Config config = ConfigFactory.parseString(hocon);
        Config resolve = config.resolve();

        System.err.println(resolve.getString("env.date"));
        System.err.println(resolve.getString("source.mysql.query"));

    }

}